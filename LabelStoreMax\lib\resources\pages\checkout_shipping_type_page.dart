//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:collection/collection.dart' show IterableExtension;
import 'package:flutter/material.dart';
import '/app/models/cart.dart';
import '/app/models/cart_line_item.dart';
import '/app/models/checkout_session.dart';
import '/app/models/shipping_type.dart';
import '/app/models/libyan_city.dart';
import '/app/services/libyan_delivery_service.dart';
import '/bootstrap/helpers.dart';
import '/resources/widgets/app_loader_widget.dart';
import '/resources/widgets/buttons.dart';
import '/resources/widgets/safearea_widget.dart';
import '/resources/widgets/velvete_ui.dart';
import 'package:nylo_framework/nylo_framework.dart';


class CheckoutShippingTypePage extends NyStatefulWidget {
  static RouteView path =
      ("/checkout-shipping-type", (_) => CheckoutShippingTypePage());

  CheckoutShippingTypePage({super.key})
      : super(child: () => _CheckoutShippingTypePageState());
}

class _CheckoutShippingTypePageState extends NyPage<CheckoutShippingTypePage> {
  bool _isShippingSupported = true, _isLoading = true;
  final List<Map<String, dynamic>> _wsShippingOptions = [];

  // Libya city selection variables
  LibyanCity? _selectedCity;
  List<LibyanCity> _libyanCities = [];
  bool _showCityDropdown = false;


  @override
  get init => () {
        _initializeLibyanCities();
        _getShippingMethods();
      };

  /// Initialize Libyan cities data
  void _initializeLibyanCities() {
    _libyanCities = LibyanCitiesData.getAllCities();
    print('✅ Loaded ${_libyanCities.length} Libyan cities for shipping');
  }

  _getShippingMethods() async {
    print('=== Getting Dynamic Shipping Methods ===');

    try {
      // CRITICAL FIX: Disable redundant WooCommerce shipping zone/method fetching
      // This was causing the dynamic cost to be overwritten to 0
      // Instead, create a single v_shipping_by_city method directly

      final checkoutSession = CheckoutSession.getInstance;
      String? country = checkoutSession.billingDetails?.shippingAddress?.customerCountry?.name;
      String? state = checkoutSession.billingDetails?.shippingAddress?.addressLine;

      print('Customer location: $country, $state');

      // DISABLED: Redundant WooCommerce API call that overrides dynamic costs
      // final dynamicShippingService = DynamicShippingService();
      // final shippingMethods = await dynamicShippingService.getShippingMethodsForLocation(
      //   country: country,
      //   state: state,
      // );

      // Instead, create the shipping method directly without WooCommerce override
      print('✅ Disabled redundant WooCommerce shipping methods fetching');

      // DECOMMISSIONED: No longer create zero-cost shipping methods here
      // CheckoutDetailsPage will be the sole authority for setting shipping costs

      // Check if customer is in Libya (now defaults to true for null)
      bool isLibyaCustomer = _isLibyaCustomer(country);

      print('🔍 Customer location analysis:');
      print('   Country: $country');
      print('   Is Libya customer: $isLibyaCustomer');
      print('🔍 DECOMMISSIONED: No longer creating placeholder shipping methods');

      // Clear shipping options - CheckoutDetailsPage will populate CheckoutSession
      _wsShippingOptions.clear();

      print('🔍 ===== SHIPPING OPTIONS SUMMARY =====');
      print('Total options: ${_wsShippingOptions.length}');
      print('DECOMMISSIONED: No auto-selection - CheckoutDetailsPage controls CheckoutSession');
      print('============================================');

      // If no dynamic methods found, add fallback options
      if (_wsShippingOptions.isEmpty) {
        print('⚠️ No dynamic shipping methods found, adding fallback options');
        _addFallbackShippingOptions();
      }

    } catch (e) {
      print("❌ Error fetching dynamic shipping methods: $e");
      print("⚠️ Adding fallback shipping options");
      _addFallbackShippingOptions();
    }

    setState(() {
      _isLoading = false;
    });
  }

  /// Check if customer is in Libya based on country information
  bool _isLibyaCustomer(String? country) {
    // If country is null, assume Libya by default, as all customers should be Libyan.
    if (country == null) return true;

    String countryLower = country.toLowerCase();
    return countryLower.contains('libya') ||
           countryLower.contains('ليبيا') ||
           countryLower == 'ly';
  }

  /// Add fallback shipping options when dynamic fetching fails
  void _addFallbackShippingOptions() {
    final checkoutSession = CheckoutSession.getInstance;
    String? country = checkoutSession.billingDetails?.shippingAddress?.customerCountry?.name;
    bool isLibyaCustomer = _isLibyaCustomer(country);

    print('🔄 Adding fallback shipping options for ${isLibyaCustomer ? "Libya" : "non-Libya"} customer');

    if (isLibyaCustomer) {
      // For Libya customers, only add the Libya-specific method
      Map<String, dynamic> libyaShippingOption = {
        "id": "v_shipping_by_city_fallback",
        "method_id": "v_shipping_by_city",
        "title": "التوصيل (درب السبيل)", // Force Arabic title
        "cost": "0.00", // Will be updated when city is selected
        "object": null
      };
      _wsShippingOptions.add(libyaShippingOption);
      print('✅ Added Libya fallback shipping method: التوصيل (درب السبيل)');
    } else {
      // For non-Libya customers, add standard options
      Map<String, dynamic> flatRateOption = {
        "id": "flat_rate",
        "method_id": "flat_rate",
        "title": "Flat Rate Delivery",
        "cost": "10.00",
        "object": null
      };
      _wsShippingOptions.add(flatRateOption);

      Map<String, dynamic> freeShippingOption = {
        "id": "free_shipping",
        "method_id": "free_shipping",
        "title": "Free Delivery",
        "cost": "0.00",
        "object": null
      };
      _wsShippingOptions.add(freeShippingOption);
      print('✅ Added ${_wsShippingOptions.length} non-Libya fallback shipping options');
    }
  }





  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        title: Text(trans("Delivery Methods")),
        automaticallyImplyLeading: false,
        centerTitle: true,
      ),
      body: SafeAreaWidget(
        child: GestureDetector(
          onTap: () => FocusScope.of(context).requestFocus(FocusNode()),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Padding(
                padding: EdgeInsets.only(top: 20),
                child: Center(
                  child: Image.asset(
                    getImageAsset('shipping_icon.png'),
                    height: 100,
                    color: (Theme.of(context).brightness == Brightness.light)
                        ? null
                        : Colors.white,
                    fit: BoxFit.fitHeight,
                    errorBuilder: (context, error, stackTrace) => Icon(Icons.local_shipping, size: 100),
                  ),
                ),
              ),
              Flexible(
                flex: 1,
                child: Container(
                  decoration: BoxDecoration(
                    color: ThemeColor.get(context).backgroundContainer,
                    borderRadius: BorderRadius.circular(10),
                    boxShadow:
                        (Theme.of(context).brightness == Brightness.light)
                            ? wsBoxShadow()
                            : null,
                  ),
                  padding: EdgeInsets.all(8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      (_isLoading
                          ? SizedBox(
                              height: 200,
                              child: AppLoaderWidget(),
                            )
                          : (_isShippingSupported
                              ? _buildShippingDisplay()
                              : Text(
                                  trans(
                                      "Delivery is not supported for your location, sorry"),
                                  style: Theme.of(context).textTheme.titleLarge,
                                  textAlign: TextAlign.center,
                                ))),

                      // City dropdown for Libya shipping method
                      if (_showCityDropdown) _buildCityDropdown(),
                      LinkButton(
                        title: trans("CANCEL"),
                        action: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build shipping display with conditional logic
  Widget _buildShippingDisplay() {
    final checkoutSession = CheckoutSession.getInstance;

    // CONDITIONAL DISPLAY: Check if shipping method is set by CheckoutDetailsPage
    if (checkoutSession.shippingType == null) {
      // No shipping method set - show message to select city first
      return Container(
        height: 200,
        padding: EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.location_city,
              size: 48,
              color: Colors.grey[400],
            ),
            SizedBox(height: 16),
            Text(
              "الرجاء تحديد مدينتك في صفحة التفاصيل أولاً",
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8),
            Text(
              "Please select your city in the details page first",
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    } else {
      // Shipping method is set - display it
      return Container(
        height: 120,
        padding: EdgeInsets.all(16),
        child: ListTile(
          leading: Icon(Icons.local_shipping, color: Colors.green, size: 32),
          title: Text(
            checkoutSession.shippingType!.object?.title ?? "التوصيل (درب السبيل)",
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.green,
            ),
          ),
          subtitle: Text(
            "${trans("Price")}: ${formatStringCurrency(total: checkoutSession.shippingType!.cost)}",
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          trailing: Icon(Icons.check_circle, color: Colors.green),
        ),
      );
    }
  }

  // DECOMMISSIONED: _handleCheckoutTapped method removed
  // CheckoutDetailsPage is now the sole authority for setting CheckoutSession.shippingType

  /// Build city dropdown widget for Libya shipping
  Widget _buildCityDropdown() {
    return Container(
      margin: EdgeInsets.all(16),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'اختر مدينتك', // "Choose your city" in Arabic
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 12),
          DropdownButtonFormField<LibyanCity>(
            value: _selectedCity,
            hint: Text('--- اختر مدينتك ---'), // "--- Choose your city ---"
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            items: _libyanCities.map((LibyanCity city) {
              return DropdownMenuItem<LibyanCity>(
                value: city,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        city.nameArabic,
                        style: TextStyle(fontSize: 14),
                      ),
                    ),
                    Text(
                      '${city.deliveryCost.toStringAsFixed(0)} د.ل',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
            onChanged: (LibyanCity? newCity) async {
              setState(() {
                _selectedCity = newCity;
              });
              if (newCity != null) {
                await _updateShippingCostForSelectedCity(newCity);
                // Trigger UI update after cost is updated
                setState(() {});
              }
            },
          ),
          if (_selectedCity != null) ...[
            SizedBox(height: 12),
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green),
              ),
              child: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green, size: 20),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'تكلفة التوصيل إلى ${_selectedCity!.nameArabic}: ${_selectedCity!.deliveryCost.toStringAsFixed(0)} د.ل',
                      style: TextStyle(
                        color: Colors.green[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _confirmLibyaShipping(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text('تأكيد التوصيل'), // "Confirm Delivery"
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      setState(() {
                        _showCityDropdown = false;
                        _selectedCity = null;
                      });
                    },
                    style: OutlinedButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text('إلغاء'), // "Cancel"
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  /// Update shipping cost based on selected Libyan city
  Future<void> _updateShippingCostForSelectedCity(LibyanCity city) async {
    print('=== Updating Shipping Cost for Selected City: ${city.nameArabic} ===');

    final deliveryService = LibyanDeliveryService();
    await deliveryService.updateShippingCostForCity(city.nameArabic);

    print('✅ Updated shipping cost for ${city.nameArabic}');
  }

  /// Confirm Libya shipping with selected city
  void _confirmLibyaShipping() {
    if (_selectedCity == null) return;

    print('=== Confirming Libya Shipping for City: ${_selectedCity!.nameArabic} ===');

    // CRITICAL FIX: Don't create a new shipping method, the dynamic cost has already been applied
    // The updateShippingCostForCity method has already updated the CheckoutSession.shippingType
    // with the correct dynamic cost, so we just need to verify it's set correctly

    final checkoutSession = CheckoutSession.getInstance;
    if (checkoutSession.shippingType != null) {
      print('✅ Using existing shipping method with dynamic cost: ${checkoutSession.shippingType!.cost} LYD');
    } else {
      print('⚠️ No shipping method found, creating fallback...');
      final deliveryService = LibyanDeliveryService();
      final cityShippingMethod = deliveryService.createLibyanCityShippingMethod(_selectedCity!.nameArabic);
      checkoutSession.shippingType = cityShippingMethod;
    }

    // Update the checkout session's billing and shipping addresses with the selected city
    if (checkoutSession.billingDetails?.billingAddress != null) {
      // Update billing address city
      checkoutSession.billingDetails!.billingAddress!.city = _selectedCity!.nameArabic;
      print('✅ Updated billing address city to: ${_selectedCity!.nameArabic}');
    }

    if (checkoutSession.billingDetails?.shippingAddress != null) {
      // Update shipping address city
      checkoutSession.billingDetails!.shippingAddress!.city = _selectedCity!.nameArabic;
      print('✅ Updated shipping address city to: ${_selectedCity!.nameArabic}');
    }

    print('✅ Libya shipping confirmed for ${_selectedCity!.nameArabic}');
    print('💰 Delivery cost: ${_selectedCity!.deliveryCost} LYD');

    // Close the page
    pop();
  }
}
