//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/resources/pages/account_order_detail_page.dart';
import '/app/services/woocommerce_service.dart';
import '/bootstrap/helpers.dart';
import 'package:nylo_framework/nylo_framework.dart';
import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';
import 'package:wp_json_api/wp_json_api.dart';
import 'package:wp_json_api/models/wp_user.dart';

class AccountDetailOrdersWidget extends StatefulWidget {
  const AccountDetailOrdersWidget({super.key});

  @override
  createState() => _AccountDetailOrdersWidgetState();
}

class _AccountDetailOrdersWidgetState
    extends NyState<AccountDetailOrdersWidget> {
  @override
  Widget view(BuildContext context) {
    return NyPullToRefresh(
        child: (context, order) {
          order as WooOrder;
          return Card(
            child: ListTile(
              contentPadding: EdgeInsets.only(
                top: 5,
                bottom: 5,
                left: 8,
                right: 6,
              ),
              title: Container(
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: Color(0xFFFCFCFC),
                      width: 1,
                    ),
                  ),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    Text(
                      "#${order.id.toString()}",
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      order.status!.name.capitalize(),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              subtitle: Padding(
                padding: const EdgeInsets.only(top: 10),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        Text(
                          formatStringCurrency(total: order.total?.toString()),
                          style: Theme.of(context)
                              .textTheme
                              .bodyMedium!
                              .copyWith(fontWeight: FontWeight.w600),
                          textAlign: TextAlign.left,
                        ),
                        Text(
                          "${order.lineItems!.length} ${trans("items")}",
                          style: Theme.of(context)
                              .textTheme
                              .bodyLarge!
                              .copyWith(fontWeight: FontWeight.w600),
                          textAlign: TextAlign.left,
                        ),
                      ],
                    ),
                    Text(
                      "${dateFormatted(
                        date: order.dateCreated!.toIso8601String(),
                        formatType: formatForDateTime(FormatType.date),
                      )}\n${dateFormatted(
                        date: order.dateCreated!.toIso8601String(),
                        formatType: formatForDateTime(FormatType.time),
                      )}",
                      textAlign: TextAlign.right,
                      style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                            fontWeight: FontWeight.w400,
                          ),
                    ),
                  ],
                ),
              ),
              trailing: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Icon(Icons.chevron_right),
                ],
              ),
              onTap: () => _viewOrderDetail(order.id),
            ),
          );
        },
        data: (page) async {
          print('🌐 ===== PROFILE ORDERS: FETCHING USER ORDERS =====');
          print('📍 Page: $page');
          print('⚠️  CRITICAL ISSUE: Orders fetching is NOT IMPLEMENTED');
          print('📋 TODO: Implement orders fetching with WooCommerce API');
          print('🔧 Expected Implementation:');
          print('   1. Get current user ID from WPJsonAPI.wpUser()');
          print('   2. Call WooCommerce API to get orders for customer');
          print('   3. Return List<WooOrder> with actual order data');
          print('❌ CURRENT: Returning empty list - no orders will be displayed');
          print('===============================================');

          try {
            // Attempt to get current user from WordPress
            print('🔍 Attempting to get current user...');
            WpUser? wpUser = await WPJsonAPI.wpUser();
            if (wpUser != null) {
              print('✅ WordPress user found: ${wpUser.id}');

              // Try to get orders for this customer
              List<WooOrder> customerOrders = await WooCommerceService().getOrders(customer: wpUser.id, page: page, perPage: 50);
              print('📦 Found ${customerOrders.length} orders for WordPress user ${wpUser.id}');

              // If no orders found for this customer, try getting recent orders (for debugging)
              if (customerOrders.isEmpty) {
                print('⚠️ No orders found for user ${wpUser.id}, fetching recent orders for debugging...');
                List<WooOrder> recentOrders = await WooCommerceService().getOrders(page: 1, perPage: 10);
                print('📦 Total recent orders in system: ${recentOrders.length}');
                for (var order in recentOrders) {
                  print('   Order ${order.id}: Customer ID = ${order.customerId}, Email = ${order.billing?.email}');
                }
              }

              return customerOrders;
            } else {
              print('❌ No user found - user not logged in');
              return <WooOrder>[];
            }
          } catch (e, stackTrace) {
            print('🌐 ===== PROFILE ORDERS: ERROR =====');
            print('❌ Exception during orders fetch: $e');
            print('📋 Stack Trace: $stackTrace');
            print('===============================================');
            return <WooOrder>[];
          }
        },
        empty: Center(
          child: Padding(
            padding: EdgeInsets.all(32),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Icon(
                  Icons.receipt_long_outlined,
                  color: Colors.grey[400],
                  size: 80,
                ),
                SizedBox(height: 16),
                Text(
                  'لا توجد طلبات',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[600],
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'لم تقم بأي طلبات حتى الآن\nابدأ التسوق لإنشاء طلبك الأول!',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
          ),
        ),
        loadingStyle: LoadingStyle.skeletonizer(
          child: SizedBox(
            height: 200,
            width: double.infinity,
            child: ListView(
              children: [
                Card(
                  child: ListTile(
                    contentPadding: EdgeInsets.only(
                      top: 5,
                      bottom: 5,
                      left: 8,
                      right: 6,
                    ),
                    title: Container(
                      decoration: BoxDecoration(
                        border: Border(
                          bottom: BorderSide(
                            color: Color(0xFFFCFCFC),
                            width: 1,
                          ),
                        ),
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: <Widget>[
                          Text(
                            "Some Text",
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text(
                            "Some Text",
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                    subtitle: Padding(
                      padding: const EdgeInsets.only(top: 10),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: <Widget>[
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: <Widget>[
                              Text(
                                formatStringCurrency(total: "Some Text"),
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium!
                                    .copyWith(fontWeight: FontWeight.w600),
                                textAlign: TextAlign.left,
                              ),
                              Text(
                                "Some Text",
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyLarge!
                                    .copyWith(fontWeight: FontWeight.w600),
                                textAlign: TextAlign.left,
                              ),
                            ],
                          ),
                          Text(
                            "Some Text",
                            textAlign: TextAlign.right,
                            style:
                                Theme.of(context).textTheme.bodyLarge!.copyWith(
                                      fontWeight: FontWeight.w400,
                                    ),
                          ),
                        ],
                      ),
                    ),
                    trailing: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        Icon(Icons.chevron_right),
                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
        ));
  }

  _viewOrderDetail(int? orderId) {
    routeTo(AccountOrderDetailPage.path, data: orderId);
  }
}
